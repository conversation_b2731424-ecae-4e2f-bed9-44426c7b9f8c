import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Camera, Upload, Mic, Send, Bot, User, ImageIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Navigation from '@/components/Navigation';
import { useSpeechRecognition } from "react-speech-kit";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

// Extend Window interface for speech recognition
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  image?: string;
}

const BotPage = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isListening, setIsListening] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [showCamera, setShowCamera] = useState(false);
  const { toast } = useToast();

  const { listen, listening, stop, supported } = useSpeechRecognition({
    onResult: (result: any) => {
      let cleanResult = '';
      if (typeof result === 'string') {
        cleanResult = result.trim();
      } else if (result && typeof result === 'object') {
        if (result.transcript) {
          cleanResult = String(result.transcript).trim();
        } else if (result.text) {
          cleanResult = String(result.text).trim();
        }
      }

      if (cleanResult && cleanResult !== '[object Object]' && cleanResult.length > 0) {
        setInputText(cleanResult);
      }
    },
    onEnd: () => {
      setIsListening(false);
    },
    onError: (error: any) => {
      console.error("Speech recognition error:", error);
      setIsListening(false);
    },
  });

  useEffect(() => {
    const generateSessionId = () => {
      return Date.now().toString(36) + Math.random().toString(36).substring(2);
    };
    const newSessionId = generateSessionId();
    setSessionId(newSessionId);
    setMessages([
      {
        id: '1',
        type: 'bot',
        content: '🌾 **Namaste! I am SugarBot, your AI farming assistant.**\n\nI can help you with:\n\n• **Disease Diagnosis** - Upload photos for instant crop analysis\n• **Farming Tips** - Get expert advice on cultivation practices\n• **Crop Management** - Receive guidance on fertilizers, irrigation, and pest control\n• **Harvest Planning** - Optimize your harvest timing for maximum yield\n• **Voice Support** - Ask questions using voice input\n\nHow can I assist you with your sugarcane farming today?',
        timestamp: new Date()
      }
    ]);
  }, []);

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageToSend = inputText;
    setInputText('');
    setIsLoading(true);

    try {
      const requestBody = {
        query: messageToSend,
        sessionId,
        timestamp: Date.now(),
        language: 'en-US'
      };

      const response = await fetch("https://n8n.onpointsoft.com/webhook/sugarcane", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      let responseContent = '';

      if (data.output) {
        responseContent = formatResponseContent(data.output);
      } else if (data.text) {
        responseContent = typeof data.text === 'string' ? data.text : String(data.text);
      } else {
        responseContent = 'No response received';
      }

      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: responseContent,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    } catch (err) {
      console.error("API Error:", err);
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: `I apologize, but I'm experiencing technical difficulties: ${err.message}. Please try again or contact support for assistance.`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
    } finally {
      setIsLoading(false);
    }
  };

  const formatResponseContent = (output: any): string => {
    if (typeof output === 'string') {
      return output;
    }

    let formattedContent = '';
    for (const [key, value] of Object.entries(output)) {
      formattedContent += `**${key}:**\n${value}\n\n`;
    }

    return formattedContent;
  };



  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid File",
          description: "Please select a valid image file",
          variant: "destructive"
        });
        return;
      }
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: "Image size should be less than 10MB",
          variant: "destructive"
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = async (e) => {
        const imageUrl = e.target?.result as string;
        const userMessage: Message = {
          id: Date.now().toString(),
          type: 'user',
          content: 'I uploaded an image of my sugarcane crop for diagnosis.',
          image: imageUrl,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, userMessage]);
        setIsLoading(true);

        try {
          const formData = new FormData();
          formData.append('image', file);
          formData.append('query', 'Analyze this sugarcane crop image for diseases and provide recommendations');
          if (sessionId) {
            formData.append('sessionId', sessionId);
          }
          formData.append('timestamp', Date.now().toString());
          formData.append('language', 'en');

          const response = await fetch("https://n8n.onpointsoft.com/webhook/sugarcane", {
            method: "POST",
            body: formData,
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API Error: ${response.status} - ${errorText}`);
          }

          const data = await response.json();
          let responseContent = '';

          if (data.output) {
            responseContent = formatResponseContent(data.output);
          } else if (data.text) {
            responseContent = typeof data.text === 'string' ? data.text : String(data.text);
          } else {
            responseContent = 'No response received';
          }

          const botResponse: Message = {
            id: (Date.now() + 1).toString(),
            type: 'bot',
            content: responseContent,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, botResponse]);
        } catch (err) {
          console.error("Image Analysis Error:", err);
          const botResponse: Message = {
            id: (Date.now() + 1).toString(),
            type: 'bot',
            content: `I apologize, but I'm having trouble analyzing your image: ${err.message}. Please try uploading the image again or contact support for assistance.`,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, botResponse]);
        } finally {
          setIsLoading(false);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        } 
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play();
        };
        setShowCamera(true);
      }
    } catch (error) {
      console.error('Camera error:', error);
      toast({
        title: "Camera Error",
        description: "Unable to access camera. Please check permissions.",
        variant: "destructive"
      });
    }
  };

  const capturePhoto = async () => {
    if (videoRef.current) {
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      const ctx = canvas.getContext('2d');
      ctx?.drawImage(videoRef.current, 0, 0);

      const imageUrl = canvas.toDataURL('image/jpeg');
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: 'I captured a photo of my sugarcane crop.',
        image: imageUrl,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);

      // Stop camera
      const stream = videoRef.current.srcObject as MediaStream;
      stream?.getTracks().forEach(track => track.stop());
      setShowCamera(false);
      setIsLoading(true);

      try {
        // Convert canvas to blob for API upload
        canvas.toBlob(async (blob) => {
          if (blob) {
            const formData = new FormData();
            formData.append('image', blob, 'captured-crop.jpg');
            formData.append('query', 'Analyze this captured sugarcane crop image for health assessment');
            if (sessionId) {
              formData.append('sessionId', sessionId);
            }
            formData.append('timestamp', Date.now().toString());
            formData.append('language', 'en');

            try {
              const response = await fetch("https://n8n.onpointsoft.com/webhook/sugarcane", {
                method: "POST",
                body: formData,
              });

              if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API Error: ${response.status} - ${errorText}`);
              }

              const data = await response.json();
              let responseContent = '';

              if (data.output) {
                responseContent = formatResponseContent(data.output);
              } else if (data.text) {
                responseContent = typeof data.text === 'string' ? data.text : String(data.text);
              } else {
                responseContent = 'No response received';
              }

              const botResponse: Message = {
                id: (Date.now() + 1).toString(),
                type: 'bot',
                content: responseContent,
                timestamp: new Date()
              };
              setMessages(prev => [...prev, botResponse]);
            } catch (err) {
              console.error("Image Analysis Error:", err);
              const botResponse: Message = {
                id: (Date.now() + 1).toString(),
                type: 'bot',
                content: `I apologize, but I'm having trouble analyzing your captured image: ${err.message}. Please try again or contact support for assistance.`,
                timestamp: new Date()
              };
              setMessages(prev => [...prev, botResponse]);
            } finally {
              setIsLoading(false);
            }
          }
        }, 'image/jpeg', 0.8);
      } catch (err) {
        console.error("Capture Error:", err);
        setIsLoading(false);
      }
    }
  };

  const toggleMic = () => {
    if (!supported) {
      toast({
        title: "Speech Recognition Not Supported",
        description: "Speech recognition is not supported in your browser. Please use Chrome, Edge, or Safari.",
        variant: "destructive"
      });
      return;
    }

    if (listening || isListening) {
      stop();
      setIsListening(false);
    } else {
      setInputText("");
      setIsListening(true);
      try {
        listen({
          lang: 'en-US',
          continuous: true,
          interimResults: true,
        });
      } catch (error) {
        console.error("Error starting speech recognition:", error);
        setIsListening(false);
        toast({
          title: "Speech Recognition Error",
          description: "Failed to start speech recognition. Please check your microphone permissions.",
          variant: "destructive"
        });
      }
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              SugarBot - Your AI Farming Assistant
            </h1>
            <p className="text-lg text-muted-foreground">
              Upload photos, ask questions, or speak in your local language for instant farming guidance
            </p>
          </div>

          <Card className="shadow-elevated">
            <CardHeader className="bg-gradient-primary rounded-t-lg">
              <CardTitle className="text-hero-fg flex items-center gap-2">
                <Bot className="h-6 w-6" />
                Chat with SugarBot
              </CardTitle>
            </CardHeader>
            
            <CardContent className="p-0">
              {/* Messages Area */}
              <div className="h-96 overflow-y-auto p-6 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex gap-3 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      <div className={`p-2 rounded-full ${message.type === 'user' ? 'bg-primary' : 'bg-earth-green'}`}>
                        {message.type === 'user' ? (
                          <User className="h-4 w-4 text-primary-foreground" />
                        ) : (
                          <Bot className="h-4 w-4 text-primary-foreground" />
                        )}
                      </div>
                      
                      <div className={`p-4 rounded-lg ${
                        message.type === 'user' 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-muted text-foreground'
                      }`}>
                        {message.image && (
                          <img 
                            src={message.image} 
                            alt="Uploaded crop" 
                            className="max-w-xs rounded-lg mb-2"
                          />
                        )}
                        <div className="text-sm">
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                              ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                              ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                              li: ({ children }) => <li className="mb-1">{children}</li>,
                              h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                              h2: ({ children }) => <h2 className="text-base font-semibold mb-2">{children}</h2>,
                              h3: ({ children }) => <h3 className="text-sm font-medium mb-1">{children}</h3>,
                              strong: ({ children }) => <strong className="font-bold">{children}</strong>,
                              code: ({ children, className }) => {
                                const isInline = !className;
                                return isInline ? (
                                  <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono">{children}</code>
                                ) : (
                                  <code className="block bg-gray-100 p-2 rounded text-xs font-mono overflow-x-auto">{children}</code>
                                );
                              },
                            }}
                          >
                            {typeof message.content === 'string' ? message.content : String(message.content || '')}
                          </ReactMarkdown>
                        </div>
                        <p className={`text-xs mt-2 ${
                          message.type === 'user' ? 'text-primary-foreground/70' : 'text-muted-foreground'
                        }`}>
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex gap-3 justify-start">
                    <div className="p-2 rounded-full bg-earth-green">
                      <Bot className="h-4 w-4 text-primary-foreground" />
                    </div>
                    <div className="p-4 rounded-lg bg-muted">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-100"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-200"></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Camera View */}
              {showCamera && (
                <div className="p-6 border-t bg-background">
                  <div className="text-center">
                    <div className="relative inline-block mb-4">
                      <video 
                        ref={videoRef} 
                        autoPlay 
                        playsInline
                        muted
                        className="w-80 h-60 rounded-lg border-2 border-primary bg-black object-cover"
                      />
                      <div className="absolute top-2 right-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs">
                        Camera Active
                      </div>
                    </div>
                    <div className="flex gap-4 justify-center">
                      <Button 
                        onClick={capturePhoto} 
                        variant="hero" 
                        size="lg"
                        className="px-6 py-3"
                      >
                        <Camera className="h-5 w-5 mr-2" />
                        Capture Photo
                      </Button>
                      <Button 
                        onClick={() => {
                          const stream = videoRef.current?.srcObject as MediaStream;
                          stream?.getTracks().forEach(track => track.stop());
                          setShowCamera(false);
                        }}
                        variant="outline"
                        size="lg"
                        className="px-6 py-3"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Input Area */}
              <div className="p-6 border-t">
                <div className="flex gap-2 mb-4">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    size="sm"
                  >
                    <Upload className="h-4 w-4" />
                    Upload
                  </Button>
                  
                  <Button
                    onClick={startCamera}
                    variant="outline"
                    size="sm"
                  >
                    <Camera className="h-4 w-4" />
                    Camera
                  </Button>
                  
                  <Button
                    onClick={toggleMic}
                    variant={(listening || isListening) ? "destructive" : "outline"}
                    size="sm"
                    disabled={!supported}
                  >
                    <Mic className="h-4 w-4" />
                    {(listening || isListening) ? 'Listening...' : 'Voice'}
                  </Button>
                </div>

                <div className="flex gap-2">
                  <Textarea
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="Ask about your sugarcane crops, diseases, fertilizers, or farming tips..."
                    className="min-h-[60px]"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputText.trim()}
                    variant="hero"
                    size="lg"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          <div className="grid md:grid-cols-3 gap-4 mt-8">
            <Card className="text-center p-4 hover:shadow-soft transition-all duration-300">
              <ImageIcon className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1">Photo Diagnosis</h3>
              <p className="text-sm text-muted-foreground">Upload crop images for instant disease identification</p>
            </Card>
            
            <Card className="text-center p-4 hover:shadow-soft transition-all duration-300">
              <Mic className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1">Voice Support</h3>
              <p className="text-sm text-muted-foreground">Speak in Kannada, Hindi, or English</p>
            </Card>
            
            <Card className="text-center p-4 hover:shadow-soft transition-all duration-300">
              <Bot className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1">24/7 Available</h3>
              <p className="text-sm text-muted-foreground">Get farming guidance anytime, anywhere</p>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BotPage;