import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Camera, Upload, Mic, Send, Bot, User, ImageIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Navigation from '@/components/Navigation';

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  image?: string;
}

const BotPage = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: '<PERSON>ast<PERSON>! I am <PERSON><PERSON><PERSON>, your AI farming assistant. I can help you with sugarcane disease diagnosis, farming tips, and crop management. How can I assist you today?',
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [showCamera, setShowCamera] = useState(false);
  const { toast } = useToast();

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    // Simulate bot response
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: getBotResponse(inputText),
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botResponse]);
      setIsLoading(false);
    }, 1500);
  };

  const getBotResponse = (input: string): string => {
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('disease') || lowerInput.includes('sick') || lowerInput.includes('problem')) {
      return 'To help diagnose diseases in your sugarcane, please upload a clear photo of the affected plant. I can identify common issues like red rot, smut, or leaf scald and provide treatment recommendations.';
    }
    
    if (lowerInput.includes('fertilizer') || lowerInput.includes('nutrition')) {
      return 'For optimal sugarcane growth, I recommend applying nitrogen-rich fertilizers during the initial growth phase. The NPK ratio should be 120:60:40 kg per hectare. Would you like specific timing recommendations based on your planting date?';
    }
    
    if (lowerInput.includes('harvest') || lowerInput.includes('cut')) {
      return 'Sugarcane is typically ready for harvest 10-12 months after planting. Look for signs like yellowing of lower leaves and high brix content (above 18%). Would you like me to help you determine the optimal harvest time?';
    }
    
    if (lowerInput.includes('water') || lowerInput.includes('irrigation')) {
      return 'Sugarcane needs 1500-2500mm of water annually. During dry periods, ensure irrigation every 7-10 days. Critical periods for watering are germination, tillering, and grand growth phases.';
    }
    
    return 'Thank you for your question! I can help you with disease diagnosis through photo analysis, fertilizer recommendations, irrigation guidance, pest control, and harvest timing. Please feel free to upload photos of your crops or ask specific questions about your sugarcane farming needs.';
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        const userMessage: Message = {
          id: Date.now().toString(),
          type: 'user',
          content: 'I uploaded an image of my sugarcane crop for diagnosis.',
          image: imageUrl,
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, userMessage]);
        
        // Simulate image analysis
        setTimeout(() => {
          const botResponse: Message = {
            id: (Date.now() + 1).toString(),
            type: 'bot',
            content: 'I can see your sugarcane crop image. Based on the analysis, I notice some yellowing on the leaves which could indicate nitrogen deficiency or red rot disease. For accurate diagnosis, please ensure the image shows the affected area clearly. I recommend:\n\n1. Apply nitrogen-rich fertilizer (Urea 46% N) at 200kg/hectare\n2. Monitor for red stripes on leaves\n3. Ensure proper drainage\n4. Consider copper-based fungicide if disease persists\n\nWould you like more specific treatment recommendations?',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, botResponse]);
        }, 2000);
      };
      reader.readAsDataURL(file);
    }
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { 
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        } 
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play();
        };
        setShowCamera(true);
      }
    } catch (error) {
      console.error('Camera error:', error);
      toast({
        title: "Camera Error",
        description: "Unable to access camera. Please check permissions.",
        variant: "destructive"
      });
    }
  };

  const capturePhoto = () => {
    if (videoRef.current) {
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      const ctx = canvas.getContext('2d');
      ctx?.drawImage(videoRef.current, 0, 0);
      
      const imageUrl = canvas.toDataURL('image/jpeg');
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: 'I captured a photo of my sugarcane crop.',
        image: imageUrl,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, userMessage]);
      
      // Stop camera
      const stream = videoRef.current.srcObject as MediaStream;
      stream?.getTracks().forEach(track => track.stop());
      setShowCamera(false);
      
      // Simulate analysis
      setTimeout(() => {
        const botResponse: Message = {
          id: (Date.now() + 1).toString(),
          type: 'bot',
          content: 'Great photo! I can analyze your sugarcane crop condition. The image shows healthy green growth with good tillering. I recommend maintaining current care practices and monitoring for any changes in leaf color or pest activity.',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, botResponse]);
      }, 2000);
    }
  };

  const startVoiceRecording = () => {
    setIsRecording(true);
    // Simulate voice recording
    setTimeout(() => {
      setIsRecording(false);
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: 'Voice message: "ನನ್ನ ಕಬ್ಬಿನ ಎಲೆಗಳಲ್ಲಿ ಹಳದಿ ಬಣ್ಣ ಕಾಣುತ್ತಿದೆ, ಏನು ಮಾಡಬೇಕು?" (My sugarcane leaves are turning yellow, what should I do?)',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, userMessage]);
      
      setTimeout(() => {
        const botResponse: Message = {
          id: (Date.now() + 1).toString(),
          type: 'bot',
          content: 'ನಮಸ್ಕಾರ! ಕಬ್ಬಿನ ಎಲೆಗಳು ಹಳದಾಗುವುದು ಸಾಮಾನ್ಯವಾಗಿ ಸಾರಜನಕ ಕೊರತೆಯಿಂದ ಆಗುತ್ತದೆ. ಈ ಕೆಳಗಿನ ಉಪಾಯಗಳನ್ನು ಅನುಸರಿಸಿ:\n\n1. ಯೂರಿಯಾ ಗೊಬ್ಬರ ಅನ್ವಯಿಸಿ (200 ಕೆಜಿ/ಹೆಕ್ಟೇರ್)\n2. ನೀರಾವರಿ ಸಮಯಕ್ಕೆ ಮಾಡಿ\n3. ಮಣ್ಣಿನ ಪರೀಕ್ಷೆ ಮಾಡಿಸಿ\n\nHello! Yellowing of sugarcane leaves is usually due to nitrogen deficiency. Follow these remedies: Apply urea fertilizer (200kg/hectare), irrigate timely, and get soil tested.',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, botResponse]);
      }, 2000);
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              SugarBot - Your AI Farming Assistant
            </h1>
            <p className="text-lg text-muted-foreground">
              Upload photos, ask questions, or speak in your local language for instant farming guidance
            </p>
          </div>

          <Card className="shadow-elevated">
            <CardHeader className="bg-gradient-primary rounded-t-lg">
              <CardTitle className="text-hero-fg flex items-center gap-2">
                <Bot className="h-6 w-6" />
                Chat with SugarBot
              </CardTitle>
            </CardHeader>
            
            <CardContent className="p-0">
              {/* Messages Area */}
              <div className="h-96 overflow-y-auto p-6 space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex gap-3 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      <div className={`p-2 rounded-full ${message.type === 'user' ? 'bg-primary' : 'bg-earth-green'}`}>
                        {message.type === 'user' ? (
                          <User className="h-4 w-4 text-primary-foreground" />
                        ) : (
                          <Bot className="h-4 w-4 text-primary-foreground" />
                        )}
                      </div>
                      
                      <div className={`p-4 rounded-lg ${
                        message.type === 'user' 
                          ? 'bg-primary text-primary-foreground' 
                          : 'bg-muted text-foreground'
                      }`}>
                        {message.image && (
                          <img 
                            src={message.image} 
                            alt="Uploaded crop" 
                            className="max-w-xs rounded-lg mb-2"
                          />
                        )}
                        <p className="whitespace-pre-wrap">{message.content}</p>
                        <p className={`text-xs mt-2 ${
                          message.type === 'user' ? 'text-primary-foreground/70' : 'text-muted-foreground'
                        }`}>
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex gap-3 justify-start">
                    <div className="p-2 rounded-full bg-earth-green">
                      <Bot className="h-4 w-4 text-primary-foreground" />
                    </div>
                    <div className="p-4 rounded-lg bg-muted">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-100"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce delay-200"></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Camera View */}
              {showCamera && (
                <div className="p-6 border-t bg-background">
                  <div className="text-center">
                    <div className="relative inline-block mb-4">
                      <video 
                        ref={videoRef} 
                        autoPlay 
                        playsInline
                        muted
                        className="w-80 h-60 rounded-lg border-2 border-primary bg-black object-cover"
                      />
                      <div className="absolute top-2 right-2 bg-primary text-primary-foreground px-2 py-1 rounded text-xs">
                        Camera Active
                      </div>
                    </div>
                    <div className="flex gap-4 justify-center">
                      <Button 
                        onClick={capturePhoto} 
                        variant="hero" 
                        size="lg"
                        className="px-6 py-3"
                      >
                        <Camera className="h-5 w-5 mr-2" />
                        Capture Photo
                      </Button>
                      <Button 
                        onClick={() => {
                          const stream = videoRef.current?.srcObject as MediaStream;
                          stream?.getTracks().forEach(track => track.stop());
                          setShowCamera(false);
                        }}
                        variant="outline"
                        size="lg"
                        className="px-6 py-3"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Input Area */}
              <div className="p-6 border-t">
                <div className="flex gap-2 mb-4">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    size="sm"
                  >
                    <Upload className="h-4 w-4" />
                    Upload
                  </Button>
                  
                  <Button
                    onClick={startCamera}
                    variant="outline"
                    size="sm"
                  >
                    <Camera className="h-4 w-4" />
                    Camera
                  </Button>
                  
                  <Button
                    onClick={startVoiceRecording}
                    variant={isRecording ? "destructive" : "outline"}
                    size="sm"
                    disabled={isRecording}
                  >
                    <Mic className="h-4 w-4" />
                    {isRecording ? 'Recording...' : 'Voice'}
                  </Button>
                </div>

                <div className="flex gap-2">
                  <Textarea
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="Ask about your sugarcane crops, diseases, fertilizers, or farming tips..."
                    className="min-h-[60px]"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputText.trim()}
                    variant="hero"
                    size="lg"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          <div className="grid md:grid-cols-3 gap-4 mt-8">
            <Card className="text-center p-4 hover:shadow-soft transition-all duration-300">
              <ImageIcon className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1">Photo Diagnosis</h3>
              <p className="text-sm text-muted-foreground">Upload crop images for instant disease identification</p>
            </Card>
            
            <Card className="text-center p-4 hover:shadow-soft transition-all duration-300">
              <Mic className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1">Voice Support</h3>
              <p className="text-sm text-muted-foreground">Speak in Kannada, Hindi, or English</p>
            </Card>
            
            <Card className="text-center p-4 hover:shadow-soft transition-all duration-300">
              <Bot className="h-8 w-8 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-foreground mb-1">24/7 Available</h3>
              <p className="text-sm text-muted-foreground">Get farming guidance anytime, anywhere</p>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BotPage;